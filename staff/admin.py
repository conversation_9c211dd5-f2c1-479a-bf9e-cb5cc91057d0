from django.contrib import admin
from django.utils.html import format_html
from django.utils import timezone
from staff.models import Staff


@admin.register(Staff)
class StaffAdmin(admin.ModelAdmin):
    # 列表页显示的字段
    list_display = (
        'uid',
        'name',
        'gender_display',
        'department_display',
        'position_display',
        'region_display',
        'phone',
        'email',
        'join_date',
        'status_display',
        'created_at'
    )

    # 列表页过滤器
    list_filter = (
        'gender',
        'department',
        'position',
        'region',
        'join_date',
        'contract_end_date',
        'deleted_at',
        'created_at',
        'updated_at'
    )

    # 搜索字段
    search_fields = (
        'name',
        'uid',
        'phone',
        'email',
        'remark'
    )

    # 详情页字段分组
    fieldsets = (
        ('基本信息', {
            'fields': ('uid', 'name', 'gender')
        }),
        ('联系方式', {
            'fields': ('phone', 'email')
        }),
        ('工作信息', {
            'fields': ('department', 'position', 'region', 'join_date', 'contract_end_date')
        }),
        ('账户信息', {
            'fields': ('password', 'last_login_time'),
            'classes': ('collapse',)
        }),
        ('离职信息', {
            'fields': ('deleted_at', 'leave_date', 'leave_reason'),
            'classes': ('collapse',)
        }),
        ('其他信息', {
            'fields': ('remark',)
        }),
        ('系统信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

    # 只读字段
    readonly_fields = ('uid', 'created_at', 'updated_at', 'last_login_time')

    # 每页显示数量
    list_per_page = 25

    # 排序
    ordering = ('-created_at',)

    # 日期层次结构
    date_hierarchy = 'join_date'

    # 自定义方法：显示性别
    def gender_display(self, obj):
        gender_colors = {
            'MALE': '#007bff',  # 蓝色
            'FEMALE': '#e83e8c',  # 粉色
        }
        color = gender_colors.get(obj.gender, '#6c757d')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color,
            obj.get_gender_display()
        )
    gender_display.short_description = '性别'
    gender_display.admin_order_field = 'gender'

    # 自定义方法：显示部门
    def department_display(self, obj):
        department_colors = {
            'DESIGN_TEAM': '#28a745',  # 绿色
            'ENGINEERING_TEAM': '#007bff',  # 蓝色
            'AFTER_SALES_TEAM': '#ffc107',  # 黄色
            'SALES_TEAM': '#dc3545',  # 红色
            'OPERATION_TEAM': '#6f42c1',  # 紫色
        }
        color = department_colors.get(obj.department, '#6c757d')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color,
            obj.get_department_display()
        )
    department_display.short_description = '部门'
    department_display.admin_order_field = 'department'

    # 自定义方法：显示职位
    def position_display(self, obj):
        # 主管级别用不同颜色显示
        leader_positions = ['DESIGN_LEADER', 'ENGINEERING_LEADER', 'AFTER_SALES_LEADER', 'SALES_LEADER']
        color = '#dc3545' if obj.position in leader_positions else '#6c757d'  # 主管用红色，其他用灰色
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color,
            obj.get_position_display()
        )
    position_display.short_description = '职位'
    position_display.admin_order_field = 'position'

    # 自定义方法：显示区域
    def region_display(self, obj):
        return format_html(
            '<span style="color: #17a2b8;">{}</span>',
            obj.get_region_display()
        )
    region_display.short_description = '区域'
    region_display.admin_order_field = 'region'

    # 自定义方法：显示启用状态
    def is_active_display(self, obj):
        if obj.is_active:
            return format_html(
                '<span style="color: #28a745; font-weight: bold;">✓ 启用</span>'
            )
        else:
            return format_html(
                '<span style="color: #dc3545; font-weight: bold;">✗ 禁用</span>'
            )
    is_active_display.short_description = '状态'
    is_active_display.admin_order_field = 'deleted_at'

    # 自定义方法：显示员工状态（在职/离职）
    def status_display(self, obj):
        if obj.deleted_at:
            return format_html(
                '<span style="color: #dc3545; font-weight: bold; background-color: #f8d7da; padding: 2px 6px; border-radius: 3px;">已离职</span>'
            )
        else:
            return format_html(
                '<span style="color: #155724; font-weight: bold; background-color: #d4edda; padding: 2px 6px; border-radius: 3px;">在职</span>'
            )
    status_display.short_description = '员工状态'
    status_display.admin_order_field = 'deleted_at'

    # 自定义查询集，显示所有员工（包括已离职的）
    def get_queryset(self, request):
        return Staff.all_objects.get_queryset()

    # 自定义操作：软删除（离职）
    def make_staff_leave(self, request, queryset):
        count = 0
        for staff in queryset:
            if not staff.deleted_at:  # 只处理在职员工
                staff.delete(leave_date=timezone.now().date(), leave_reason='管理员操作离职')
                count += 1
        self.message_user(request, f'成功将 {count} 名员工设置为离职状态。')
    make_staff_leave.short_description = '将选中员工设置为离职'

    # 自定义操作：禁用员工
    def disable_staff(self, request, queryset):
        count = queryset.filter(is_active=True, deleted_at__isnull=True).update(is_active=False)
        self.message_user(request, f'成功禁用 {count} 名员工。')
    disable_staff.short_description = '禁用选中员工'

    # 自定义操作：启用员工
    def enable_staff(self, request, queryset):
        count = queryset.filter(is_active=False, deleted_at__isnull=True).update(is_active=True)
        self.message_user(request, f'成功启用 {count} 名员工。')
    enable_staff.short_description = '启用选中员工'

    # 注册自定义操作
    actions = ['make_staff_leave', 'disable_staff', 'enable_staff']

    # 自定义保存方法
    def save_model(self, request, obj, form, change):
        # 如果是新建员工且设置了密码，进行加密
        if not change and obj.password:
            obj.set_password(obj.password)
        # 如果是修改且密码字段被更改，进行加密
        elif change and 'password' in form.changed_data and obj.password:
            obj.set_password(obj.password)
        super().save_model(request, obj, form, change)